#!/bin/bash

# Docker Container Restart Script
# This script stops and starts all Docker containers to effectively restart them.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if Docker is available
check_docker() {
    print_status $BLUE "🐳 Docker Container Restart Script"
    print_status $BLUE "========================================"
    
    if ! command -v docker &> /dev/null; then
        print_status $RED "Docker command not found. Please install Docker."
        exit 1
    fi
    
    if ! docker version &> /dev/null; then
        print_status $RED "Docker is not running. Please start Docker daemon."
        exit 1
    fi
    
    print_status $GREEN "Docker is available and running"
}

# Function to get running containers
get_running_containers() {
    docker ps -q
}

# Function to get all containers
get_all_containers() {
    docker ps -aq
}

# Function to stop all running containers
stop_containers() {
    local running_containers
    running_containers=$(get_running_containers)
    
    if [ -z "$running_containers" ]; then
        print_status $YELLOW "No running containers to stop"
        return 0
    fi
    
    local container_count
    container_count=$(echo "$running_containers" | wc -l)
    
    print_status $YELLOW "Stopping $container_count running containers..."
    
    if docker stop $running_containers; then
        print_status $GREEN "Successfully stopped $container_count containers"
        return 0
    else
        print_status $RED "Failed to stop some containers"
        return 1
    fi
}

# Function to start all containers
start_containers() {
    local all_containers
    all_containers=$(get_all_containers)
    
    if [ -z "$all_containers" ]; then
        print_status $YELLOW "No containers found to start"
        return 0
    fi
    
    local container_count
    container_count=$(echo "$all_containers" | wc -l)
    
    print_status $BLUE "🚀 Starting $container_count containers..."
    
    if docker start $all_containers; then
        print_status $GREEN "Successfully started $container_count containers"
        return 0
    else
        print_status $RED "Failed to start some containers"
        return 1
    fi
}

# Function to show container status
show_status() {
    print_status $BLUE "\n📊 Current container status:"
    
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null; then
        echo ""
    else
        print_status $YELLOW "No containers running or failed to get status"
    fi
}

# Function to wait with countdown
wait_with_countdown() {
    local seconds=$1
    local message=$2
    
    print_status $YELLOW "$message"
    for ((i=seconds; i>0; i--)); do
        echo -ne "\r⏳ Waiting... ${i}s remaining"
        sleep 1
    done
    echo -e "\r⏳ Waiting... Done!     "
}

# Main function
main() {
    # Check Docker availability
    check_docker
    
    # Stop all running containers
    print_status $BLUE "\n🔄 Step 1: Stopping all running containers..."
    if ! stop_containers; then
        print_status $RED "Failed to stop containers. Exiting."
        exit 1
    fi
    
    # Wait for containers to fully stop
    wait_with_countdown 3 "⏳ Waiting for containers to fully stop..."
    
    # Start all containers
    print_status $BLUE "\n🔄 Step 2: Starting all containers..."
    if ! start_containers; then
        print_status $RED "❌ Failed to start containers. Exiting."
        exit 1
    fi
    
    # Wait for containers to initialize
    wait_with_countdown 5 "⏳ Waiting for containers to initialize..."
    
    # Show final status
    show_status
    
    print_status $GREEN "\n✅ Docker container restart completed successfully!"
}

# Handle script interruption
trap 'print_status $RED "\n❌ Script interrupted. Some containers may be in inconsistent state."; exit 1' INT TERM

# Run main function
main "$@"
