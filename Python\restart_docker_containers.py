#!/usr/bin/env python3
"""
Docker Container Restart Script
This script stops and starts all Docker containers to effectively restart them.
"""

import subprocess
import sys
import time
from typing import List, Tuple


def run_command(command: List[str]) -> Tuple[bool, str, str]:
    """
    Execute a command and return success status, stdout, and stderr.
    
    Args:
        command: List of command parts to execute
        
    Returns:
        Tuple of (success, stdout, stderr)
    """
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out after 5 minutes"
    except Exception as e:
        return False, "", str(e)


def check_docker_available() -> bool:
    """Check if Docker is available and running."""
    success, stdout, stderr = run_command(["docker", "version"])
    if not success:
        print(f"❌ Docker is not available or not running: {stderr}")
        return False
    print("✅ Docker is available and running")
    return True


def get_all_containers() -> List[str]:
    """Get list of all container IDs."""
    success, stdout, stderr = run_command(["docker", "ps", "-aq"])
    if not success:
        print(f"❌ Failed to get container list: {stderr}")
        return []
    
    containers = [line.strip() for line in stdout.split('\n') if line.strip()]
    return containers


def get_running_containers() -> List[str]:
    """Get list of running container IDs."""
    success, stdout, stderr = run_command(["docker", "ps", "-q"])
    if not success:
        print(f"❌ Failed to get running container list: {stderr}")
        return []
    
    containers = [line.strip() for line in stdout.split('\n') if line.strip()]
    return containers


def stop_all_containers() -> bool:
    """Stop all running containers."""
    running_containers = get_running_containers()
    
    if not running_containers:
        print("ℹ️  No running containers to stop")
        return True
    
    print(f"🛑 Stopping {len(running_containers)} running containers...")
    success, stdout, stderr = run_command(["docker", "stop"] + running_containers)
    
    if success:
        print(f"✅ Successfully stopped {len(running_containers)} containers")
        return True
    else:
        print(f"❌ Failed to stop containers: {stderr}")
        return False


def start_all_containers() -> bool:
    """Start all containers."""
    all_containers = get_all_containers()
    
    if not all_containers:
        print("ℹ️  No containers found to start")
        return True
    
    print(f"🚀 Starting {len(all_containers)} containers...")
    success, stdout, stderr = run_command(["docker", "start"] + all_containers)
    
    if success:
        print(f"✅ Successfully started {len(all_containers)} containers")
        return True
    else:
        print(f"❌ Failed to start containers: {stderr}")
        return False


def show_container_status():
    """Display current container status."""
    print("\n📊 Current container status:")
    success, stdout, stderr = run_command(["docker", "ps", "--format", "table {{.Names}}\t{{.Status}}\t{{.Ports}}"])
    
    if success and stdout:
        print(stdout)
    else:
        print("No containers running or failed to get status")


def main():
    """Main function to restart all Docker containers."""
    print("🐳 Docker Container Restart Script")
    print("=" * 40)
    
    # Check if Docker is available
    if not check_docker_available():
        sys.exit(1)
    
    # Stop all running containers
    print("\n🔄 Step 1: Stopping all running containers...")
    if not stop_all_containers():
        print("❌ Failed to stop containers. Exiting.")
        sys.exit(1)
    
    # Wait a moment for containers to fully stop
    print("⏳ Waiting 3 seconds for containers to fully stop...")
    time.sleep(3)
    
    # Start all containers
    print("\n🔄 Step 2: Starting all containers...")
    if not start_all_containers():
        print("❌ Failed to start containers. Exiting.")
        sys.exit(1)
    
    # Wait a moment for containers to initialize
    print("⏳ Waiting 5 seconds for containers to initialize...")
    time.sleep(5)
    
    # Show final status
    show_container_status()
    
    print("\n✅ Docker container restart completed successfully!")


if __name__ == "__main__":
    main()
